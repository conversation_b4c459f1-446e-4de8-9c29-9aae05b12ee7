#!/usr/bin/env python3
"""
Test script for the حساباتي (Hasabati) Personal Finance API
"""

import requests
import json

BASE_URL = "http://localhost:5000"

def test_registration():
    """Test user registration"""
    print("Testing user registration...")
    
    data = {
        "username": "testuser",
        "email": "<EMAIL>", 
        "password": "testpass123"
    }
    
    response = requests.post(f"{BASE_URL}/api/register", json=data)
    print(f"Registration Status: {response.status_code}")
    print(f"Response: {response.text}")
    return response.status_code == 201

def test_login():
    """Test user login"""
    print("\nTesting user login...")
    
    data = {
        "username": "testuser",
        "password": "testpass123"
    }
    
    response = requests.post(f"{BASE_URL}/api/login", json=data)
    print(f"Login Status: {response.status_code}")
    
    if response.status_code == 200:
        token = response.json().get('token')
        print(f"Login successful! Token received.")
        return token
    else:
        print(f"Login failed: {response.text}")
        return None

def test_dashboard(token):
    """Test dashboard endpoint"""
    print("\nTesting dashboard...")
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/api/dashboard", headers=headers)
    
    print(f"Dashboard Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Net Balance: {data.get('net_balance', 0)}")
        print(f"Total Income: {data.get('total_income', 0)}")
        print(f"Total Expense: {data.get('total_expense', 0)}")
        return True
    else:
        print(f"Dashboard failed: {response.text}")
        return False

def test_add_transaction(token):
    """Test adding a transaction"""
    print("\nTesting add transaction...")
    
    headers = {"Authorization": f"Bearer {token}"}
    data = {
        "type": "income",
        "category": "راتب",
        "amount": 5000.0,
        "description": "راتب شهر يونيو",
        "date": "2025-06-05"
    }
    
    response = requests.post(f"{BASE_URL}/api/transactions", json=data, headers=headers)
    print(f"Add Transaction Status: {response.status_code}")
    
    if response.status_code == 201:
        transaction = response.json()
        print(f"Transaction added: {transaction.get('category')} - {transaction.get('amount')}")
        return transaction.get('id')
    else:
        print(f"Add transaction failed: {response.text}")
        return None

def test_get_transactions(token):
    """Test getting transactions"""
    print("\nTesting get transactions...")
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/api/transactions", headers=headers)
    
    print(f"Get Transactions Status: {response.status_code}")
    if response.status_code == 200:
        transactions = response.json()
        print(f"Found {len(transactions)} transactions")
        for t in transactions:
            print(f"  - {t.get('category')}: {t.get('amount')} ({t.get('type')})")
        return True
    else:
        print(f"Get transactions failed: {response.text}")
        return False

def main():
    """Run all tests"""
    print("=== حساباتي API Test Suite ===\n")
    
    # Test registration
    if not test_registration():
        print("Registration test failed!")
        return
    
    # Test login
    token = test_login()
    if not token:
        print("Login test failed!")
        return
    
    # Test dashboard
    if not test_dashboard(token):
        print("Dashboard test failed!")
        return
    
    # Test add transaction
    transaction_id = test_add_transaction(token)
    if not transaction_id:
        print("Add transaction test failed!")
        return
    
    # Test get transactions
    if not test_get_transactions(token):
        print("Get transactions test failed!")
        return
    
    # Test dashboard again to see updated data
    print("\nTesting dashboard with updated data...")
    test_dashboard(token)
    
    print("\n=== All tests completed successfully! ===")

if __name__ == "__main__":
    main()
