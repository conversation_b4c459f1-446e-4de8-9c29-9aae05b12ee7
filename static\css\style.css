/* Custom CSS for حساباتي - Personal Finance App */

/* RTL Support */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    text-align: right;
}

/* Dark Mode Variables */
:root {
    --bg-color: #ffffff;
    --text-color: #212529;
    --card-bg: #ffffff;
    --border-color: #dee2e6;
    --navbar-bg: #0d6efd;
    --table-bg: #ffffff;
    --table-stripe: #f8f9fa;
}

[data-theme="dark"] {
    --bg-color: #1a1a1a;
    --text-color: #ffffff;
    --card-bg: #2d2d2d;
    --border-color: #404040;
    --navbar-bg: #0d6efd;
    --table-bg: #2d2d2d;
    --table-stripe: #3d3d3d;
}

/* Apply dark mode styles */
[data-theme="dark"] body {
    background-color: var(--bg-color);
    color: var(--text-color);
}

[data-theme="dark"] .card {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .table {
    background-color: var(--table-bg);
    color: var(--text-color);
}

[data-theme="dark"] .table-striped > tbody > tr:nth-of-type(odd) > td {
    background-color: var(--table-stripe);
}

[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .form-control:focus,
[data-theme="dark"] .form-select:focus {
    background-color: var(--card-bg);
    border-color: #0d6efd;
    color: var(--text-color);
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

[data-theme="dark"] .modal-content {
    background-color: var(--card-bg);
    color: var(--text-color);
}

[data-theme="dark"] .nav-tabs .nav-link {
    color: var(--text-color);
    border-color: var(--border-color);
}

[data-theme="dark"] .nav-tabs .nav-link.active {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

/* Custom Styles */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.card {
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.table th {
    border-top: none;
    font-weight: 600;
    background-color: #f8f9fa;
}

[data-theme="dark"] .table th {
    background-color: var(--table-stripe);
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
}

.modal-content {
    border-radius: 15px;
    border: none;
}

.form-control,
.form-select {
    border-radius: 8px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus,
.form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Badge Styles */
.badge {
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
}

/* Chart Container */
#expenseChart {
    max-height: 300px;
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Alert Styles */
.alert {
    border-radius: 10px;
    border: none;
}

/* Tab Styles */
.nav-tabs {
    border-bottom: 2px solid #dee2e6;
}

.nav-tabs .nav-link {
    border: none;
    border-radius: 10px 10px 0 0;
    margin-bottom: -2px;
    font-weight: 500;
}

.nav-tabs .nav-link.active {
    border-bottom: 2px solid #0d6efd;
    background-color: transparent;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
    
    .btn {
        padding: 0.375rem 0.5rem;
        font-size: 0.875rem;
    }
    
    .navbar-brand {
        font-size: 1.25rem;
    }
}

/* Animation Classes */
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

[data-theme="dark"] ::-webkit-scrollbar-track {
    background: #2d2d2d;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
    background: #555;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
    background: #777;
}

/* Print Styles */
@media print {
    .navbar,
    .btn,
    .modal {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #000;
    }
}
