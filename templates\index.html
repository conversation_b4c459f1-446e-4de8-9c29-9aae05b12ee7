<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حساباتي - إدارة الأموال الشخصية</title>
    
    <!-- Bootstrap 5.3 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div id="app">
        <!-- Navigation Bar -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary" v-if="isLoggedIn">
            <div class="container">
                <a class="navbar-brand" href="#">
                    <i class="bi bi-wallet2"></i>
                    حساباتي
                </a>
                <div class="navbar-nav ms-auto">
                    <button class="btn btn-outline-light me-2" @click="toggleDarkMode">
                        <i class="bi" :class="isDarkMode ? 'bi-sun' : 'bi-moon'"></i>
                    </button>
                    <button class="btn btn-outline-light" @click="logout">
                        <i class="bi bi-box-arrow-right"></i>
                        تسجيل الخروج
                    </button>
                </div>
            </div>
        </nav>

        <!-- Login/Register View -->
        <div v-if="!isLoggedIn" class="container mt-5">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header text-center">
                            <h3>
                                <i class="bi bi-wallet2"></i>
                                حساباتي
                            </h3>
                        </div>
                        <div class="card-body">
                            <!-- Login Form -->
                            <div v-if="!showRegister">
                                <h5 class="card-title text-center mb-4">تسجيل الدخول</h5>
                                <form @submit.prevent="login">
                                    <div class="mb-3">
                                        <label class="form-label">اسم المستخدم</label>
                                        <input type="text" class="form-control" v-model="loginForm.username" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">كلمة المرور</label>
                                        <input type="password" class="form-control" v-model="loginForm.password" required>
                                    </div>
                                    <button type="submit" class="btn btn-primary w-100" :disabled="isLoading">
                                        <span v-if="isLoading" class="spinner-border spinner-border-sm me-2"></span>
                                        تسجيل الدخول
                                    </button>
                                </form>
                                <div class="text-center mt-3">
                                    <button class="btn btn-link" @click="showRegister = true">
                                        إنشاء حساب جديد
                                    </button>
                                </div>
                            </div>

                            <!-- Register Form -->
                            <div v-else>
                                <h5 class="card-title text-center mb-4">إنشاء حساب جديد</h5>
                                <form @submit.prevent="register">
                                    <div class="mb-3">
                                        <label class="form-label">اسم المستخدم</label>
                                        <input type="text" class="form-control" v-model="registerForm.username" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" v-model="registerForm.email" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">كلمة المرور</label>
                                        <input type="password" class="form-control" v-model="registerForm.password" required>
                                    </div>
                                    <button type="submit" class="btn btn-success w-100" :disabled="isLoading">
                                        <span v-if="isLoading" class="spinner-border spinner-border-sm me-2"></span>
                                        إنشاء الحساب
                                    </button>
                                </form>
                                <div class="text-center mt-3">
                                    <button class="btn btn-link" @click="showRegister = false">
                                        العودة لتسجيل الدخول
                                    </button>
                                </div>
                            </div>

                            <!-- Alert Messages -->
                            <div v-if="alertMessage" class="alert mt-3" :class="alertType" role="alert" v-text="alertMessage">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard View -->
        <div v-if="isLoggedIn" class="container mt-4">
            <!-- Dashboard Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card text-white bg-success">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">الرصيد الصافي</h6>
                                    <h4 v-text="formatCurrency(dashboardData.net_balance)"></h4>
                                </div>
                                <i class="bi bi-wallet2 fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card text-white bg-primary">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">إجمالي الدخل</h6>
                                    <h4 v-text="formatCurrency(dashboardData.total_income)"></h4>
                                </div>
                                <i class="bi bi-arrow-up-circle fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card text-white bg-danger">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">إجمالي المصروفات</h6>
                                    <h4 v-text="formatCurrency(dashboardData.total_expense)"></h4>
                                </div>
                                <i class="bi bi-arrow-down-circle fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card text-white bg-warning">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">الديون المستحقة</h6>
                                    <h4 v-text="formatCurrency(dashboardData.total_debts)"></h4>
                                </div>
                                <i class="bi bi-exclamation-triangle fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chart Section -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>توزيع المصروفات</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="expenseChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>الإحصائيات الشهرية</h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <h6>الدخل الشهري</h6>
                                    <h4 class="text-success" v-text="formatCurrency(dashboardData.monthly_income)"></h4>
                                </div>
                                <div class="col-6">
                                    <h6>المصروفات الشهرية</h6>
                                    <h4 class="text-danger" v-text="formatCurrency(dashboardData.monthly_expense)"></h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tabs Navigation -->
            <ul class="nav nav-tabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link" :class="{ active: activeTab === 'transactions' }" 
                            @click="activeTab = 'transactions'" type="button">
                        <i class="bi bi-list-ul"></i>
                        المعاملات
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" :class="{ active: activeTab === 'debts' }" 
                            @click="activeTab = 'debts'" type="button">
                        <i class="bi bi-exclamation-triangle"></i>
                        الديون
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" :class="{ active: activeTab === 'receivables' }" 
                            @click="activeTab = 'receivables'" type="button">
                        <i class="bi bi-cash-coin"></i>
                        المستحقات
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content mt-3">
                <!-- Transactions Tab -->
                <div v-if="activeTab === 'transactions'" class="tab-pane fade show active">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5>المعاملات</h5>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#transactionModal">
                            <i class="bi bi-plus"></i>
                            إضافة معاملة
                        </button>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>النوع</th>
                                    <th>الفئة</th>
                                    <th>المبلغ</th>
                                    <th>الوصف</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="transaction in transactions" :key="transaction.id">
                                    <td v-text="formatDate(transaction.date)"></td>
                                    <td>
                                        <span class="badge" :class="transaction.type === 'income' ? 'bg-success' : 'bg-danger'">
                                            <span v-if="transaction.type === 'income'">دخل</span>
                                            <span v-else>مصروف</span>
                                        </span>
                                    </td>
                                    <td v-text="transaction.category"></td>
                                    <td v-text="formatCurrency(transaction.amount)"></td>
                                    <td v-text="transaction.description"></td>
                                    <td>
                                        <button class="btn btn-sm btn-danger" @click="deleteTransaction(transaction.id)">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Debts Tab -->
                <div v-if="activeTab === 'debts'" class="tab-pane fade show active">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5>الديون</h5>
                        <button class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#debtModal">
                            <i class="bi bi-plus"></i>
                            إضافة دين
                        </button>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الدائن</th>
                                    <th>المبلغ</th>
                                    <th>تاريخ الاستحقاق</th>
                                    <th>الوصف</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="debt in debts" :key="debt.id">
                                    <td v-text="debt.creditor_name"></td>
                                    <td v-text="formatCurrency(debt.amount)"></td>
                                    <td v-text="debt.due_date ? formatDate(debt.due_date) : 'غير محدد'"></td>
                                    <td v-text="debt.description"></td>
                                    <td>
                                        <span class="badge" :class="debt.is_paid ? 'bg-success' : 'bg-warning'">
                                            <span v-if="debt.is_paid">مدفوع</span>
                                            <span v-else>غير مدفوع</span>
                                        </span>
                                    </td>
                                    <td>
                                        <button v-if="!debt.is_paid" class="btn btn-sm btn-success me-1"
                                                @click="markDebtAsPaid(debt.id)">
                                            <i class="bi bi-check"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" @click="deleteDebt(debt.id)">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Receivables Tab -->
                <div v-if="activeTab === 'receivables'" class="tab-pane fade show active">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5>المستحقات</h5>
                        <button class="btn btn-info" data-bs-toggle="modal" data-bs-target="#receivableModal">
                            <i class="bi bi-plus"></i>
                            إضافة مستحق
                        </button>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>المدين</th>
                                    <th>المبلغ</th>
                                    <th>تاريخ الاستحقاق</th>
                                    <th>الوصف</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="receivable in receivables" :key="receivable.id">
                                    <td v-text="receivable.debtor_name"></td>
                                    <td v-text="formatCurrency(receivable.amount)"></td>
                                    <td v-text="receivable.due_date ? formatDate(receivable.due_date) : 'غير محدد'"></td>
                                    <td v-text="receivable.description"></td>
                                    <td>
                                        <span class="badge" :class="receivable.is_received ? 'bg-success' : 'bg-info'">
                                            <span v-if="receivable.is_received">مستلم</span>
                                            <span v-else>غير مستلم</span>
                                        </span>
                                    </td>
                                    <td>
                                        <button v-if="!receivable.is_received" class="btn btn-sm btn-success me-1"
                                                @click="markReceivableAsReceived(receivable.id)">
                                            <i class="bi bi-check"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" @click="deleteReceivable(receivable.id)">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transaction Modal -->
        <div class="modal fade" id="transactionModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">إضافة معاملة جديدة</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form @submit.prevent="addTransaction">
                            <div class="mb-3">
                                <label class="form-label">نوع المعاملة</label>
                                <select class="form-select" v-model="transactionForm.type" required>
                                    <option value="">اختر النوع</option>
                                    <option value="income">دخل</option>
                                    <option value="expense">مصروف</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">الفئة</label>
                                <input type="text" class="form-control" v-model="transactionForm.category" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">المبلغ</label>
                                <input type="number" step="0.01" class="form-control" v-model="transactionForm.amount" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">التاريخ</label>
                                <input type="date" class="form-control" v-model="transactionForm.date" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">الوصف</label>
                                <textarea class="form-control" v-model="transactionForm.description" rows="3"></textarea>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="submit" class="btn btn-primary">إضافة</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Debt Modal -->
        <div class="modal fade" id="debtModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">إضافة دين جديد</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form @submit.prevent="addDebt">
                            <div class="mb-3">
                                <label class="form-label">اسم الدائن</label>
                                <input type="text" class="form-control" v-model="debtForm.creditor_name" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">المبلغ</label>
                                <input type="number" step="0.01" class="form-control" v-model="debtForm.amount" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">تاريخ الاستحقاق</label>
                                <input type="date" class="form-control" v-model="debtForm.due_date">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">الوصف</label>
                                <textarea class="form-control" v-model="debtForm.description" rows="3"></textarea>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="submit" class="btn btn-warning">إضافة</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Receivable Modal -->
        <div class="modal fade" id="receivableModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">إضافة مستحق جديد</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form @submit.prevent="addReceivable">
                            <div class="mb-3">
                                <label class="form-label">اسم المدين</label>
                                <input type="text" class="form-control" v-model="receivableForm.debtor_name" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">المبلغ</label>
                                <input type="number" step="0.01" class="form-control" v-model="receivableForm.amount" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">تاريخ الاستحقاق</label>
                                <input type="date" class="form-control" v-model="receivableForm.due_date">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">الوصف</label>
                                <textarea class="form-control" v-model="receivableForm.description" rows="3"></textarea>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="submit" class="btn btn-info">إضافة</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5.3 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Vue.js 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Custom JavaScript -->
    <script src="{{ url_for('static', filename='js/script.js') }}" defer></script>
</body>
</html>
