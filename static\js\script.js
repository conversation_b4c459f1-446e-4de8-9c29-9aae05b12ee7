// Vue.js 3 Application for حساباتي - Personal Finance App

const { createApp } = Vue;

createApp({
    data() {
        return {
            // Authentication
            isLoggedIn: false,
            userToken: null,
            showRegister: false,
            isLoading: false,
            
            // Forms
            loginForm: {
                username: '',
                password: ''
            },
            registerForm: {
                username: '',
                email: '',
                password: ''
            },
            transactionForm: {
                type: '',
                category: '',
                amount: '',
                date: new Date().toISOString().split('T')[0],
                description: ''
            },
            debtForm: {
                creditor_name: '',
                amount: '',
                due_date: '',
                description: ''
            },
            receivableForm: {
                debtor_name: '',
                amount: '',
                due_date: '',
                description: ''
            },
            
            // Data
            dashboardData: {
                net_balance: 0,
                total_income: 0,
                total_expense: 0,
                total_debts: 0,
                total_receivables: 0,
                monthly_income: 0,
                monthly_expense: 0
            },
            transactions: [],
            debts: [],
            receivables: [],
            
            // UI State
            activeTab: 'transactions',
            alertMessage: '',
            alertType: '',
            isDarkMode: false,
            
            // Chart
            expenseChart: null
        }
    },
    
    methods: {
        // Authentication Methods
        async login() {
            this.isLoading = true;
            this.clearAlert();
            
            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(this.loginForm)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    this.userToken = data.token;
                    this.isLoggedIn = true;
                    localStorage.setItem('userToken', data.token);
                    await this.fetchDashboardData();
                    await this.fetchAllData();
                    this.showAlert('تم تسجيل الدخول بنجاح', 'alert-success');
                } else {
                    this.showAlert(data.message || 'فشل في تسجيل الدخول', 'alert-danger');
                }
            } catch (error) {
                this.showAlert('حدث خطأ في الاتصال', 'alert-danger');
                console.error('Login error:', error);
            } finally {
                this.isLoading = false;
            }
        },
        
        async register() {
            this.isLoading = true;
            this.clearAlert();
            
            try {
                const response = await fetch('/api/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(this.registerForm)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    this.showAlert('تم إنشاء الحساب بنجاح، يمكنك الآن تسجيل الدخول', 'alert-success');
                    this.showRegister = false;
                    this.resetForms();
                } else {
                    this.showAlert(data.message || 'فشل في إنشاء الحساب', 'alert-danger');
                }
            } catch (error) {
                this.showAlert('حدث خطأ في الاتصال', 'alert-danger');
                console.error('Register error:', error);
            } finally {
                this.isLoading = false;
            }
        },
        
        logout() {
            this.isLoggedIn = false;
            this.userToken = null;
            localStorage.removeItem('userToken');
            this.resetForms();
            this.resetData();
            this.showAlert('تم تسجيل الخروج بنجاح', 'alert-info');
        },
        
        // API Methods
        async apiRequest(url, options = {}) {
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.userToken}`
                }
            };
            
            const mergedOptions = {
                ...defaultOptions,
                ...options,
                headers: {
                    ...defaultOptions.headers,
                    ...options.headers
                }
            };
            
            const response = await fetch(url, mergedOptions);
            
            if (response.status === 401) {
                this.logout();
                throw new Error('Unauthorized');
            }
            
            return response;
        },
        
        async fetchDashboardData() {
            try {
                const response = await this.apiRequest('/api/dashboard');
                if (response.ok) {
                    this.dashboardData = await response.json();
                    this.updateChart();
                }
            } catch (error) {
                console.error('Error fetching dashboard data:', error);
            }
        },
        
        async fetchTransactions() {
            try {
                const response = await this.apiRequest('/api/transactions');
                if (response.ok) {
                    this.transactions = await response.json();
                }
            } catch (error) {
                console.error('Error fetching transactions:', error);
            }
        },
        
        async fetchDebts() {
            try {
                const response = await this.apiRequest('/api/debts');
                if (response.ok) {
                    this.debts = await response.json();
                }
            } catch (error) {
                console.error('Error fetching debts:', error);
            }
        },
        
        async fetchReceivables() {
            try {
                const response = await this.apiRequest('/api/receivables');
                if (response.ok) {
                    this.receivables = await response.json();
                }
            } catch (error) {
                console.error('Error fetching receivables:', error);
            }
        },
        
        async fetchAllData() {
            await Promise.all([
                this.fetchTransactions(),
                this.fetchDebts(),
                this.fetchReceivables()
            ]);
        },

        // Transaction Methods
        async addTransaction() {
            try {
                const response = await this.apiRequest('/api/transactions', {
                    method: 'POST',
                    body: JSON.stringify(this.transactionForm)
                });

                if (response.ok) {
                    this.showAlert('تم إضافة المعاملة بنجاح', 'alert-success');
                    this.resetTransactionForm();
                    this.closeModal('transactionModal');
                    await this.fetchDashboardData();
                    await this.fetchTransactions();
                } else {
                    const data = await response.json();
                    this.showAlert(data.message || 'فشل في إضافة المعاملة', 'alert-danger');
                }
            } catch (error) {
                this.showAlert('حدث خطأ في إضافة المعاملة', 'alert-danger');
                console.error('Error adding transaction:', error);
            }
        },

        async deleteTransaction(id) {
            if (confirm('هل أنت متأكد من حذف هذه المعاملة؟')) {
                try {
                    const response = await this.apiRequest(`/api/transactions/${id}`, {
                        method: 'DELETE'
                    });

                    if (response.ok) {
                        this.showAlert('تم حذف المعاملة بنجاح', 'alert-success');
                        await this.fetchDashboardData();
                        await this.fetchTransactions();
                    } else {
                        const data = await response.json();
                        this.showAlert(data.message || 'فشل في حذف المعاملة', 'alert-danger');
                    }
                } catch (error) {
                    this.showAlert('حدث خطأ في حذف المعاملة', 'alert-danger');
                    console.error('Error deleting transaction:', error);
                }
            }
        },

        // Debt Methods
        async addDebt() {
            try {
                const response = await this.apiRequest('/api/debts', {
                    method: 'POST',
                    body: JSON.stringify(this.debtForm)
                });

                if (response.ok) {
                    this.showAlert('تم إضافة الدين بنجاح', 'alert-success');
                    this.resetDebtForm();
                    this.closeModal('debtModal');
                    await this.fetchDashboardData();
                    await this.fetchDebts();
                } else {
                    const data = await response.json();
                    this.showAlert(data.message || 'فشل في إضافة الدين', 'alert-danger');
                }
            } catch (error) {
                this.showAlert('حدث خطأ في إضافة الدين', 'alert-danger');
                console.error('Error adding debt:', error);
            }
        },

        async deleteDebt(id) {
            if (confirm('هل أنت متأكد من حذف هذا الدين؟')) {
                try {
                    const response = await this.apiRequest(`/api/debts/${id}`, {
                        method: 'DELETE'
                    });

                    if (response.ok) {
                        this.showAlert('تم حذف الدين بنجاح', 'alert-success');
                        await this.fetchDashboardData();
                        await this.fetchDebts();
                    } else {
                        const data = await response.json();
                        this.showAlert(data.message || 'فشل في حذف الدين', 'alert-danger');
                    }
                } catch (error) {
                    this.showAlert('حدث خطأ في حذف الدين', 'alert-danger');
                    console.error('Error deleting debt:', error);
                }
            }
        },

        async markDebtAsPaid(id) {
            try {
                const response = await this.apiRequest(`/api/debts/${id}`, {
                    method: 'PUT',
                    body: JSON.stringify({ is_paid: true })
                });

                if (response.ok) {
                    this.showAlert('تم تحديث حالة الدين بنجاح', 'alert-success');
                    await this.fetchDashboardData();
                    await this.fetchDebts();
                } else {
                    const data = await response.json();
                    this.showAlert(data.message || 'فشل في تحديث حالة الدين', 'alert-danger');
                }
            } catch (error) {
                this.showAlert('حدث خطأ في تحديث حالة الدين', 'alert-danger');
                console.error('Error updating debt:', error);
            }
        },

        // Receivable Methods
        async addReceivable() {
            try {
                const response = await this.apiRequest('/api/receivables', {
                    method: 'POST',
                    body: JSON.stringify(this.receivableForm)
                });

                if (response.ok) {
                    this.showAlert('تم إضافة المستحق بنجاح', 'alert-success');
                    this.resetReceivableForm();
                    this.closeModal('receivableModal');
                    await this.fetchDashboardData();
                    await this.fetchReceivables();
                } else {
                    const data = await response.json();
                    this.showAlert(data.message || 'فشل في إضافة المستحق', 'alert-danger');
                }
            } catch (error) {
                this.showAlert('حدث خطأ في إضافة المستحق', 'alert-danger');
                console.error('Error adding receivable:', error);
            }
        },

        async deleteReceivable(id) {
            if (confirm('هل أنت متأكد من حذف هذا المستحق؟')) {
                try {
                    const response = await this.apiRequest(`/api/receivables/${id}`, {
                        method: 'DELETE'
                    });

                    if (response.ok) {
                        this.showAlert('تم حذف المستحق بنجاح', 'alert-success');
                        await this.fetchDashboardData();
                        await this.fetchReceivables();
                    } else {
                        const data = await response.json();
                        this.showAlert(data.message || 'فشل في حذف المستحق', 'alert-danger');
                    }
                } catch (error) {
                    this.showAlert('حدث خطأ في حذف المستحق', 'alert-danger');
                    console.error('Error deleting receivable:', error);
                }
            }
        },

        async markReceivableAsReceived(id) {
            try {
                const response = await this.apiRequest(`/api/receivables/${id}`, {
                    method: 'PUT',
                    body: JSON.stringify({ is_received: true })
                });

                if (response.ok) {
                    this.showAlert('تم تحديث حالة المستحق بنجاح', 'alert-success');
                    await this.fetchDashboardData();
                    await this.fetchReceivables();
                } else {
                    const data = await response.json();
                    this.showAlert(data.message || 'فشل في تحديث حالة المستحق', 'alert-danger');
                }
            } catch (error) {
                this.showAlert('حدث خطأ في تحديث حالة المستحق', 'alert-danger');
                console.error('Error updating receivable:', error);
            }
        },

        // Utility Methods
        formatCurrency(amount) {
            return new Intl.NumberFormat('ar-SA', {
                style: 'currency',
                currency: 'SAR',
                minimumFractionDigits: 2
            }).format(amount || 0);
        },

        formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA');
        },

        showAlert(message, type) {
            this.alertMessage = message;
            this.alertType = type;
            setTimeout(() => {
                this.clearAlert();
            }, 5000);
        },

        clearAlert() {
            this.alertMessage = '';
            this.alertType = '';
        },

        closeModal(modalId) {
            const modal = bootstrap.Modal.getInstance(document.getElementById(modalId));
            if (modal) {
                modal.hide();
            }
        },

        resetForms() {
            this.loginForm = { username: '', password: '' };
            this.registerForm = { username: '', email: '', password: '' };
            this.resetTransactionForm();
            this.resetDebtForm();
            this.resetReceivableForm();
        },

        resetTransactionForm() {
            this.transactionForm = {
                type: '',
                category: '',
                amount: '',
                date: new Date().toISOString().split('T')[0],
                description: ''
            };
        },

        resetDebtForm() {
            this.debtForm = {
                creditor_name: '',
                amount: '',
                due_date: '',
                description: ''
            };
        },

        resetReceivableForm() {
            this.receivableForm = {
                debtor_name: '',
                amount: '',
                due_date: '',
                description: ''
            };
        },

        resetData() {
            this.dashboardData = {
                net_balance: 0,
                total_income: 0,
                total_expense: 0,
                total_debts: 0,
                total_receivables: 0,
                monthly_income: 0,
                monthly_expense: 0
            };
            this.transactions = [];
            this.debts = [];
            this.receivables = [];
        },

        // Dark Mode
        toggleDarkMode() {
            this.isDarkMode = !this.isDarkMode;
            document.documentElement.setAttribute('data-theme', this.isDarkMode ? 'dark' : 'light');
            localStorage.setItem('darkMode', this.isDarkMode);
        },

        // Chart Methods
        initChart() {
            const ctx = document.getElementById('expenseChart');
            if (ctx) {
                this.expenseChart = new Chart(ctx, {
                    type: 'pie',
                    data: {
                        labels: ['الطعام', 'النقل', 'الترفيه', 'الفواتير', 'أخرى'],
                        datasets: [{
                            data: [0, 0, 0, 0, 0],
                            backgroundColor: [
                                '#FF6384',
                                '#36A2EB',
                                '#FFCE56',
                                '#4BC0C0',
                                '#9966FF'
                            ],
                            borderWidth: 2,
                            borderColor: '#ffffff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    padding: 20,
                                    usePointStyle: true
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const value = context.parsed;
                                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                                        return `${context.label}: ${percentage}%`;
                                    }
                                }
                            }
                        }
                    }
                });
            }
        },

        updateChart() {
            if (this.expenseChart && this.transactions.length > 0) {
                // Calculate expense categories
                const expenseTransactions = this.transactions.filter(t => t.type === 'expense');
                const categories = {};

                expenseTransactions.forEach(transaction => {
                    const category = transaction.category || 'أخرى';
                    categories[category] = (categories[category] || 0) + transaction.amount;
                });

                // Update chart data
                const labels = Object.keys(categories);
                const data = Object.values(categories);

                if (labels.length > 0) {
                    this.expenseChart.data.labels = labels;
                    this.expenseChart.data.datasets[0].data = data;
                    this.expenseChart.update();
                }
            }
        }
    },

    async mounted() {
        // Check for saved token
        const savedToken = localStorage.getItem('userToken');
        if (savedToken) {
            this.userToken = savedToken;
            this.isLoggedIn = true;

            try {
                await this.fetchDashboardData();
                await this.fetchAllData();
            } catch (error) {
                console.error('Error loading data:', error);
                this.logout();
            }
        }

        // Check for dark mode preference
        const savedDarkMode = localStorage.getItem('darkMode');
        if (savedDarkMode === 'true') {
            this.isDarkMode = true;
            document.documentElement.setAttribute('data-theme', 'dark');
        }

        // Initialize chart after DOM is ready
        this.$nextTick(() => {
            this.initChart();
        });
    }
}).mount('#app');
