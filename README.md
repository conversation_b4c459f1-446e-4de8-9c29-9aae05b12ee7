# حساباتي - Personal Finance Management System

A comprehensive personal finance management web application built with Flask (Python) backend and Vue.js 3 frontend, featuring a fully responsive Arabic UI with RTL support.

## 🌟 Features

### Core Functionality
- **User Authentication**: Secure registration and login with JWT tokens
- **Dashboard**: Real-time financial overview with key metrics
- **Transaction Management**: Track income and expenses with categories
- **Debt Tracking**: Monitor money you owe to others
- **Receivables Management**: Track money others owe you
- **Data Visualization**: Interactive charts showing expense distribution

### UI/UX Features
- **Arabic RTL Interface**: Fully localized Arabic interface with right-to-left layout
- **Responsive Design**: Works seamlessly on desktop, tablet, and mobile devices
- **Dark Mode**: Toggle between light and dark themes
- **Modern UI**: Clean, intuitive interface using Bootstrap 5
- **Real-time Updates**: Live data updates without page refresh

## 🛠️ Technology Stack

### Backend
- **Python 3.9+**
- **Flask 2.3.3** - Web framework
- **Flask-SQLAlchemy 3.0.5** - ORM for database operations
- **Flask-CORS 4.0.0** - Cross-origin resource sharing
- **PyJWT 2.8.0** - JSON Web Token authentication
- **SQLite** - Lightweight database

### Frontend
- **HTML5** with semantic markup
- **CSS3** with custom styles and dark mode
- **JavaScript ES6+**
- **Vue.js 3** - Progressive JavaScript framework
- **Bootstrap 5.3** - CSS framework for responsive design
- **Chart.js** - Data visualization library
- **Bootstrap Icons** - Icon library

## 📁 Project Structure

```
حساباتي/
├── app.py                 # Main Flask application
├── models.py              # Database models (not used - models in app.py)
├── requirements.txt       # Python dependencies
├── test_api.py           # API testing script
├── database.db           # SQLite database (created automatically)
├── instance/             # Instance folder for database
├── static/
│   ├── css/
│   │   └── style.css     # Custom CSS with dark mode
│   └── js/
│       └── script.js     # Vue.js application logic
└── templates/
    └── index.html        # Main HTML template
```

## 🚀 Installation & Setup

### Prerequisites
- Python 3.9 or higher
- pip (Python package installer)

### Step 1: Clone or Download
Download the project files to your local machine.

### Step 2: Install Dependencies
```bash
pip install -r requirements.txt
```

### Step 3: Run the Application
```bash
python app.py
```

The application will start on `http://localhost:5000`

### Step 4: Access the Application
Open your web browser and navigate to `http://localhost:5000`

## 📖 Usage Guide

### Getting Started
1. **Register**: Create a new account with username, email, and password
2. **Login**: Sign in with your credentials
3. **Dashboard**: View your financial overview

### Managing Transactions
1. Click "إضافة معاملة" (Add Transaction)
2. Select transaction type (دخل/مصروف - Income/Expense)
3. Enter category, amount, date, and description
4. Save the transaction

### Managing Debts
1. Go to "الديون" (Debts) tab
2. Click "إضافة دين" (Add Debt)
3. Enter creditor name, amount, due date, and description
4. Mark as paid when settled

### Managing Receivables
1. Go to "المستحقات" (Receivables) tab
2. Click "إضافة مستحق" (Add Receivable)
3. Enter debtor name, amount, due date, and description
4. Mark as received when collected

## 🔧 API Endpoints

### Authentication
- `POST /api/register` - User registration
- `POST /api/login` - User login

### Dashboard
- `GET /api/dashboard` - Get financial summary (requires auth)

### Transactions
- `GET /api/transactions` - Get all transactions (requires auth)
- `POST /api/transactions` - Add new transaction (requires auth)
- `DELETE /api/transactions/<id>` - Delete transaction (requires auth)

### Debts
- `GET /api/debts` - Get all debts (requires auth)
- `POST /api/debts` - Add new debt (requires auth)
- `PUT /api/debts/<id>` - Update debt status (requires auth)
- `DELETE /api/debts/<id>` - Delete debt (requires auth)

### Receivables
- `GET /api/receivables` - Get all receivables (requires auth)
- `POST /api/receivables` - Add new receivable (requires auth)
- `PUT /api/receivables/<id>` - Update receivable status (requires auth)
- `DELETE /api/receivables/<id>` - Delete receivable (requires auth)

## 🧪 Testing

Run the included test script to verify API functionality:

```bash
python test_api.py
```

This will test:
- User registration and login
- Dashboard data retrieval
- Transaction creation and retrieval
- Data persistence and updates

## 🎨 Customization

### Themes
The application includes a built-in dark mode toggle. You can customize colors and themes by modifying `static/css/style.css`.

### Language
While the interface is in Arabic, you can modify text strings in `templates/index.html` and `static/js/script.js` for localization.

### Database
The application uses SQLite by default. To use a different database, modify the `SQLALCHEMY_DATABASE_URI` in `app.py`.

## 🔒 Security Features

- **Password Hashing**: Passwords are hashed using SHA-256
- **JWT Authentication**: Secure token-based authentication
- **CORS Protection**: Cross-origin request handling
- **Input Validation**: Server-side validation for all inputs

## 📱 Browser Compatibility

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is open source and available under the MIT License.

## 🆘 Support

For issues or questions:
1. Check the browser console for JavaScript errors
2. Check the Flask server logs for backend errors
3. Ensure all dependencies are installed correctly
4. Verify the database file has proper permissions

## 🔮 Future Enhancements

- Export data to PDF/Excel
- Budget planning and tracking
- Recurring transactions
- Multi-currency support
- Mobile app version
- Advanced reporting and analytics
- Email notifications for due dates
