from flask import Flask, request, jsonify, render_template
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS
import jwt
from datetime import datetime, timedelta
from functools import wraps
import os
import hashlib

# Initialize Flask app
app = Flask(__name__)

# Configuration
app.config['SECRET_KEY'] = 'your-secret-key-change-in-production'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///database.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize extensions
db = SQLAlchemy(app)
CORS(app)

# Simple password hashing function
def hash_password(password):
    return hashlib.sha256(password.encode()).hexdigest()

def check_password(password, hashed):
    return hashlib.sha256(password.encode()).hexdigest() == hashed

# Define models directly here
class User(db.Model):
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(128), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    transactions = db.relationship('Transaction', backref='user', lazy=True, cascade='all, delete-orphan')
    debts = db.relationship('Debt', backref='user', lazy=True, cascade='all, delete-orphan')
    receivables = db.relationship('Receivable', backref='user', lazy=True, cascade='all, delete-orphan')

    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'created_at': self.created_at.isoformat()
        }

class Transaction(db.Model):
    __tablename__ = 'transactions'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    type = db.Column(db.String(20), nullable=False)  # 'income' or 'expense'
    category = db.Column(db.String(50), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    description = db.Column(db.Text)
    date = db.Column(db.DateTime, default=datetime.utcnow)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'type': self.type,
            'category': self.category,
            'amount': self.amount,
            'description': self.description,
            'date': self.date.isoformat(),
            'created_at': self.created_at.isoformat()
        }

class Debt(db.Model):
    __tablename__ = 'debts'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    creditor_name = db.Column(db.String(100), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    description = db.Column(db.Text)
    due_date = db.Column(db.DateTime)
    is_paid = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'creditor_name': self.creditor_name,
            'amount': self.amount,
            'description': self.description,
            'due_date': self.due_date.isoformat() if self.due_date else None,
            'is_paid': self.is_paid,
            'created_at': self.created_at.isoformat()
        }

class Receivable(db.Model):
    __tablename__ = 'receivables'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    debtor_name = db.Column(db.String(100), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    description = db.Column(db.Text)
    due_date = db.Column(db.DateTime)
    is_received = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'debtor_name': self.debtor_name,
            'amount': self.amount,
            'description': self.description,
            'due_date': self.due_date.isoformat() if self.due_date else None,
            'is_received': self.is_received,
            'created_at': self.created_at.isoformat()
        }

# Create database tables
with app.app_context():
    db.create_all()

# JWT Token decorator
def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers.get('Authorization')
        
        if not token:
            return jsonify({'message': 'Token is missing!'}), 401
        
        try:
            # Remove 'Bearer ' prefix if present
            if token.startswith('Bearer '):
                token = token[7:]
            
            data = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])
            current_user = User.query.get(data['user_id'])
            
            if not current_user:
                return jsonify({'message': 'Token is invalid!'}), 401
                
        except jwt.ExpiredSignatureError:
            return jsonify({'message': 'Token has expired!'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'message': 'Token is invalid!'}), 401
        
        return f(current_user, *args, **kwargs)
    
    return decorated

# Routes
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/register', methods=['POST'])
def register():
    try:
        data = request.get_json()
        
        # Validate input
        if not data.get('username') or not data.get('email') or not data.get('password'):
            return jsonify({'message': 'Missing required fields'}), 400
        
        # Check if user already exists
        if User.query.filter_by(username=data['username']).first():
            return jsonify({'message': 'Username already exists'}), 400
        
        if User.query.filter_by(email=data['email']).first():
            return jsonify({'message': 'Email already exists'}), 400
        
        # Hash password
        password_hash = hash_password(data['password'])
        
        # Create new user
        new_user = User(
            username=data['username'],
            email=data['email'],
            password_hash=password_hash
        )
        
        db.session.add(new_user)
        db.session.commit()
        
        return jsonify({'message': 'User registered successfully'}), 201
        
    except Exception as e:
        return jsonify({'message': 'Registration failed', 'error': str(e)}), 500

@app.route('/api/login', methods=['POST'])
def login():
    try:
        data = request.get_json()
        
        if not data.get('username') or not data.get('password'):
            return jsonify({'message': 'Missing username or password'}), 400
        
        user = User.query.filter_by(username=data['username']).first()
        
        if user and check_password(data['password'], user.password_hash):
            # Generate JWT token
            token = jwt.encode({
                'user_id': user.id,
                'exp': datetime.utcnow() + timedelta(days=7)
            }, app.config['SECRET_KEY'], algorithm='HS256')
            
            return jsonify({
                'token': token,
                'user': user.to_dict()
            }), 200
        
        return jsonify({'message': 'Invalid credentials'}), 401
        
    except Exception as e:
        return jsonify({'message': 'Login failed', 'error': str(e)}), 500

@app.route('/api/dashboard')
@token_required
def dashboard(current_user):
    try:
        # Calculate summary data
        transactions = Transaction.query.filter_by(user_id=current_user.id).all()
        debts = Debt.query.filter_by(user_id=current_user.id, is_paid=False).all()
        receivables = Receivable.query.filter_by(user_id=current_user.id, is_received=False).all()
        
        total_income = sum(t.amount for t in transactions if t.type == 'income')
        total_expense = sum(t.amount for t in transactions if t.type == 'expense')
        total_debts = sum(d.amount for d in debts)
        total_receivables = sum(r.amount for r in receivables)
        
        net_balance = total_income - total_expense - total_debts + total_receivables
        
        # Monthly data (current month)
        current_month = datetime.now().month
        current_year = datetime.now().year
        
        monthly_transactions = [t for t in transactions 
                              if t.date.month == current_month and t.date.year == current_year]
        
        monthly_income = sum(t.amount for t in monthly_transactions if t.type == 'income')
        monthly_expense = sum(t.amount for t in monthly_transactions if t.type == 'expense')
        
        return jsonify({
            'net_balance': net_balance,
            'total_income': total_income,
            'total_expense': total_expense,
            'total_debts': total_debts,
            'total_receivables': total_receivables,
            'monthly_income': monthly_income,
            'monthly_expense': monthly_expense,
            'transactions_count': len(transactions),
            'debts_count': len(debts),
            'receivables_count': len(receivables)
        }), 200
        
    except Exception as e:
        return jsonify({'message': 'Failed to fetch dashboard data', 'error': str(e)}), 500

@app.route('/api/transactions', methods=['GET', 'POST'])
@token_required
def transactions(current_user):
    if request.method == 'GET':
        try:
            transactions = Transaction.query.filter_by(user_id=current_user.id).order_by(Transaction.date.desc()).all()
            return jsonify([t.to_dict() for t in transactions]), 200
        except Exception as e:
            return jsonify({'message': 'Failed to fetch transactions', 'error': str(e)}), 500

    elif request.method == 'POST':
        try:
            data = request.get_json()

            if not all(k in data for k in ['type', 'category', 'amount']):
                return jsonify({'message': 'Missing required fields'}), 400

            new_transaction = Transaction(
                user_id=current_user.id,
                type=data['type'],
                category=data['category'],
                amount=float(data['amount']),
                description=data.get('description', ''),
                date=datetime.fromisoformat(data['date']) if data.get('date') else datetime.utcnow()
            )

            db.session.add(new_transaction)
            db.session.commit()

            return jsonify(new_transaction.to_dict()), 201

        except Exception as e:
            return jsonify({'message': 'Failed to create transaction', 'error': str(e)}), 500

@app.route('/api/transactions/<int:transaction_id>', methods=['DELETE'])
@token_required
def delete_transaction(current_user, transaction_id):
    try:
        transaction = Transaction.query.filter_by(id=transaction_id, user_id=current_user.id).first()

        if not transaction:
            return jsonify({'message': 'Transaction not found'}), 404

        db.session.delete(transaction)
        db.session.commit()

        return jsonify({'message': 'Transaction deleted successfully'}), 200

    except Exception as e:
        return jsonify({'message': 'Failed to delete transaction', 'error': str(e)}), 500

@app.route('/api/debts', methods=['GET', 'POST'])
@token_required
def debts(current_user):
    if request.method == 'GET':
        try:
            debts = Debt.query.filter_by(user_id=current_user.id).order_by(Debt.created_at.desc()).all()
            return jsonify([d.to_dict() for d in debts]), 200
        except Exception as e:
            return jsonify({'message': 'Failed to fetch debts', 'error': str(e)}), 500

    elif request.method == 'POST':
        try:
            data = request.get_json()

            if not all(k in data for k in ['creditor_name', 'amount']):
                return jsonify({'message': 'Missing required fields'}), 400

            new_debt = Debt(
                user_id=current_user.id,
                creditor_name=data['creditor_name'],
                amount=float(data['amount']),
                description=data.get('description', ''),
                due_date=datetime.fromisoformat(data['due_date']) if data.get('due_date') else None
            )

            db.session.add(new_debt)
            db.session.commit()

            return jsonify(new_debt.to_dict()), 201

        except Exception as e:
            return jsonify({'message': 'Failed to create debt', 'error': str(e)}), 500

@app.route('/api/debts/<int:debt_id>', methods=['DELETE', 'PUT'])
@token_required
def manage_debt(current_user, debt_id):
    try:
        debt = Debt.query.filter_by(id=debt_id, user_id=current_user.id).first()

        if not debt:
            return jsonify({'message': 'Debt not found'}), 404

        if request.method == 'DELETE':
            db.session.delete(debt)
            db.session.commit()
            return jsonify({'message': 'Debt deleted successfully'}), 200

        elif request.method == 'PUT':
            data = request.get_json()
            if 'is_paid' in data:
                debt.is_paid = data['is_paid']
                db.session.commit()
                return jsonify(debt.to_dict()), 200

    except Exception as e:
        return jsonify({'message': 'Failed to manage debt', 'error': str(e)}), 500

@app.route('/api/receivables', methods=['GET', 'POST'])
@token_required
def receivables(current_user):
    if request.method == 'GET':
        try:
            receivables = Receivable.query.filter_by(user_id=current_user.id).order_by(Receivable.created_at.desc()).all()
            return jsonify([r.to_dict() for r in receivables]), 200
        except Exception as e:
            return jsonify({'message': 'Failed to fetch receivables', 'error': str(e)}), 500

    elif request.method == 'POST':
        try:
            data = request.get_json()

            if not all(k in data for k in ['debtor_name', 'amount']):
                return jsonify({'message': 'Missing required fields'}), 400

            new_receivable = Receivable(
                user_id=current_user.id,
                debtor_name=data['debtor_name'],
                amount=float(data['amount']),
                description=data.get('description', ''),
                due_date=datetime.fromisoformat(data['due_date']) if data.get('due_date') else None
            )

            db.session.add(new_receivable)
            db.session.commit()

            return jsonify(new_receivable.to_dict()), 201

        except Exception as e:
            return jsonify({'message': 'Failed to create receivable', 'error': str(e)}), 500

@app.route('/api/receivables/<int:receivable_id>', methods=['DELETE', 'PUT'])
@token_required
def manage_receivable(current_user, receivable_id):
    try:
        receivable = Receivable.query.filter_by(id=receivable_id, user_id=current_user.id).first()

        if not receivable:
            return jsonify({'message': 'Receivable not found'}), 404

        if request.method == 'DELETE':
            db.session.delete(receivable)
            db.session.commit()
            return jsonify({'message': 'Receivable deleted successfully'}), 200

        elif request.method == 'PUT':
            data = request.get_json()
            if 'is_received' in data:
                receivable.is_received = data['is_received']
                db.session.commit()
                return jsonify(receivable.to_dict()), 200

    except Exception as e:
        return jsonify({'message': 'Failed to manage receivable', 'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True)
